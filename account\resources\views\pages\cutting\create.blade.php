@extends('layouts.master')

@section('main_content')
    <style>
        body {
            background-color: #ece9d8;
        }

        .cards {
            background: linear-gradient(to bottom, #dbe8f3, #bcd2ee);
            border: 2px solid #5b8bd8;
            border-radius: 8px;
            box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.3);
            padding: 25px;
            font-family: Tahoma, sans-serif;
            color: #000080;
        }

        h1 {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #003399;
        }

        .form-label {
            font-weight: bold;
            color: #000060;
        }

        .form-control {
            border: 1px solid #7f9db9;
            border-radius: 4px;
            padding: 8px;
            font-size: 16px;
        }

        .form-control:focus {
            border-color: #1c5db6;
            box-shadow: 0 0 5px #1c5db6;
        }

        .btn-success {
            background-color: #9acd32;
            color: #000;
            border: 1px solid #7f9db9;
            font-weight: bold;
            box-shadow: inset 0 0 3px #fff;
        }

        .btn-success:hover {
            background-color: #b4e64d;
            color: #000;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 4px;
        }

        .erp-table-section {
            padding: 30px 0;
        }
    </style>

    <div class="erp-table-section">
        <div class="container-fluid">
            <div class="cards">
                <h1>Create Cutting</h1>
                <form action="{{ route('cuttings.store') }}" method="POST">
                    @csrf

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="production_date" class="form-label">Production Date:</label>
                                <input type="date" name="production_date" id="production_date" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label for="metre" class="form-label">Metre:</label>
                                <input type="number" name="metre" id="metre" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label for="lay" class="form-label">Lay:</label>
                                <input type="number" name="lay" id="lay" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label for="color" class="form-label">Color:</label>
                                <input type="text" name="color" id="color" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label for="size_30" class="form-label">Size 30:</label>
                                <input type="number" name="size_30" id="size_30" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label for="size_32" class="form-label">Size 32:</label>
                                <input type="number" name="size_32" id="size_32" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label for="size_34" class="form-label">Size 34:</label>
                                <input type="number" name="size_34" id="size_34" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label for="size_36" class="form-label">Size 36:</label>
                                <input type="number" name="size_36" id="size_36" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label for="size_38" class="form-label">Size 38:</label>
                                <input type="number" name="size_38" id="size_38" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="size_40" class="form-label">Size 40:</label>
                                <input type="number" name="size_80" id="size_80" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label for="size_42" class="form-label">Size 42:</label>
                                <input type="number" name="size_42" id="size_42" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label for="size_44" class="form-label">Size 44:</label>
                                <input type="number" name="size_44" id="size_44" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label for="size_46" class="form-label">Size 46:</label>
                                <input type="number" name="size_46" id="size_46" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label for="size_48" class="form-label">Size 48:</label>
                                <input type="number" name="size_48" id="size_48" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label for="size_50" class="form-label">Size 50:</label>
                                <input type="number" name="size_50" id="size_50" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label for="short_cutting" class="form-label">Short Cutting:</label>
                                <input type="text" name="short_cutting" id="short_cutting" class="form-control">
                            </div>
                            <div class="mb-3">
                                <label for="total" class="form-label">Total:</label>
                                <input type="number" name="total" id="total" class="form-control" step="0.01">
                            </div>
                            <div class="mb-3">
                                <label for="order_id" class="form-label">Order ID:</label>
                                <input type="number" name="order_id" id="order_id" class="form-control">
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-success">Create</button>
                    <script>
    function calculateTotal() {
        let total = 0;
        const sizeFields = [
            'size_30', 'size_32', 'size_34', 'size_36', 'size_38',
            'size_80', 'size_42', 'size_44', 'size_46', 'size_48', 'size_50'
        ];

        sizeFields.forEach(id => {
            const input = document.getElementById(id);
            if (input) {
                const value = parseInt(input.value) || 0;
                total += value;
            }
        });

        document.getElementById('total').value = total;
    }

    window.addEventListener('DOMContentLoaded', () => {
        const inputs = document.querySelectorAll('input[id^="size_"]');
        inputs.forEach(input => {
            input.addEventListener('input', calculateTotal);
        });
    });
</script>

                </form>
            </div>
        </div>
    </div>
@endsection
