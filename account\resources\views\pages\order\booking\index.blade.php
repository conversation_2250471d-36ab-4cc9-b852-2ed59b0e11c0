@extends('layouts.app')

@section('content')

<style>
  /* Container padding */
  .container-fluid {
    padding: 16px 20px;
    max-width: 900px;
    margin: auto;
  }

  /* Segmented control */
  .segmented-control {
    display: flex;
    justify-content: center;
    margin-bottom: 24px;
    gap: 12px;
  }
  .segment-btn {
    flex: 1 1 auto;
    padding: 10px 0;
    font-weight: 600;
    font-size: 16px;
    border-radius: 24px;
    border: 1.5px solid #007AFF;
    background: white;
    color: #007AFF;
    cursor: pointer;
    transition: background-color 0.3s ease, color 0.3s ease;
    user-select: none;
  }
  .segment-btn.active {
    background-color: #007AFF;
    color: white;
    box-shadow: 0 2px 8px rgba(0,122,255,0.3);
  }

  /* Card container */
  .cards {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    padding: 24px;
  }

  /* Flex grid for inputs */
  .grid-5 {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 20px;
  }
  .input-wrapper {
    flex: 1 1 180px;
    min-width: 140px;
    display: flex;
    flex-direction: column;
  }

  /* Labels */
  .input-wrapper label {
    font-weight: 600;
    margin-bottom: 6px;
    font-size: 14px;
    color: #333;
  }

  /* Inputs and selects */
  input[type="text"],
  input[type="date"],
  select {
    border-radius: 12px;
    border: 1px solid #ddd;
    padding: 12px 16px;
    font-size: 16px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    outline: none;
    transition: border-color 0.3s ease;
  }
  input[type="text"]:focus,
  input[type="date"]:focus,
  select:focus {
    border-color: #007AFF;
    box-shadow: 0 0 6px rgba(0,122,255,0.4);
  }

  /* Submit button */
  .add-report-btn {
    border-radius: 24px;
    background: #007AFF;
    color: white;
    font-weight: 600;
    padding: 12px 28px;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-size: 16px;
    display: inline-block;
  }
  .add-report-btn:hover {
    background: #005BB5;
  }

  /* Tab panes */
  .tab-pane {
    display: none;
  }
  .tab-pane.active,
  .tab-pane.show {
    display: block;
  }

  /* Responsive table */
  table {
    width: 100%;
    border-collapse: collapse;
  }
  th, td {
    padding: 12px 10px;
    text-align: left;
    border-bottom: 1px solid #eee;
    font-size: 15px;
    color: #444;
  }
  th {
    font-weight: 700;
    background: #f9f9f9;
  }

  /* Responsive */
  @media (max-width: 600px) {
    .grid-5 {
      flex-direction: column;
    }
    .input-wrapper {
      min-width: 100%;
    }
  }
</style>

<div class="container-fluid">
  <h2 class="text-center mb-4">{{ __('Booking Page') }}</h2>

  <div class="segmented-control">
    @can('bookings-read')
    <button class="segment-btn active" data-tab="booking-list">{{ __('Booking List') }}</button>
    @endcan
    @can('bookings-create')
    <button class="segment-btn" data-tab="add-booking">{{ __('Add New Booking') }}</button>
    @endcan
  </div>

  {{-- Booking List Tab --}}
  @can('bookings-read')
  <div class="tab-pane active" id="booking-list">
    <div class="cards">
      @include('bookings.list') {{-- Your existing bookings list blade --}}
    </div>
  </div>
  @endcan

  {{-- Add New Booking Tab --}}
  @can('bookings-create')
  <div class="tab-pane" id="add-booking">
    <form action="{{ route('bookings.store') }}" method="POST">
      @csrf

      <div class="grid-5">
        <div class="input-wrapper">
          <label for="name">{{ __('Customer Name') }}</label>
          <input type="text" id="name" name="name" placeholder="{{ __('Customer Name') }}" required>
        </div>
        <div class="input-wrapper">
          <label for="phone">{{ __('Customer Phone') }}</label>
          <input type="text" id="phone" name="phone" placeholder="{{ __('Customer Phone') }}" required>
        </div>
        <div class="input-wrapper">
          <label for="table">{{ __('Table Number') }}</label>
          <select id="table" name="table" required>
            <option value="" disabled selected>{{ __('Select Table') }}</option>
            @for ($i = 1; $i <= 50; $i++)
              <option value="{{ $i }}">{{ $i }}</option>
            @endfor
          </select>
        </div>
        <div class="input-wrapper">
          <label for="date">{{ __('Booking Date') }}</label>
          <input type="date" id="date" name="date" required>
        </div>
        <div class="input-wrapper">
          <label for="time">{{ __('Booking Time') }}</label>
          <input type="text" id="time" name="time" placeholder="HH:MM" required>
        </div>
      </div>

      <button type="submit" class="add-report-btn">{{ __('Add Booking') }}</button>
    </form>
  </div>
  @endcan
</div>

<script>
  // Tab switching logic
  document.querySelectorAll('.segment-btn').forEach(btn => {
    btn.addEventListener('click', () => {
      // Remove active class from all buttons
      document.querySelectorAll('.segment-btn').forEach(b => b.classList.remove('active'));
      btn.classList.add('active');

      const selectedTab = btn.getAttribute('data-tab');

      // Show the correct tab pane
      document.querySelectorAll('.tab-pane').forEach(pane => {
        if (pane.id === selectedTab) {
          pane.classList.add('active', 'show');
        } else {
          pane.classList.remove('active', 'show');
        }
      });
    });
  });
</script>

@endsection
