"use strict";

/** dashboard start */
function getDashboardData() {
    var url = $('#get-dashboard').val();
    $.ajax({
        type: "GET",
        url: url,
        dataType: "json",
        success: function (res) {
            $('#total_order').text(res.total_order);
            $('#running_order_qty').text(res.running_order_qty);
            $('#pending_order').text(res.pending_order);
            $('#weekly_order_value').text(res.weekly_order_value);
            $('#monthly_order_value').text(res.monthly_order_value);
            $('#current_year_value').text(res.current_year_value);

            $('#total_cash').text(res.total_cash);
            $('#total_bank_balance').text(res.total_bank_balance);
            $('#supplier_due').text(res.supplier_due);
            $('#monthly_expense').text(res.monthly_expense);
            $('#debit_transaction').text(res.debit_transaction);
            $('#credit_transaction').text(res.credit_transaction);
        }
    });
}

$('.earning-expense-month').on('change', function () {
    let year = $(this).val();
    getYearlyStatistics(year)
})

// Function to convert month index to month name
function getMonthNameFromIndex(index) {
    var months = [
        "January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December"
    ];
    return months[index - 1];
}

function getYearlyStatistics(year = new Date().getFullYear()) {
    var url = $('#yearly-statistics-url').val();
    $.ajax({
        type: "GET",
        url: url += '?year=' + year,
        dataType: "json",
        success: function (res) {
            var earnings = res.earnings;
            var expenses = res.expenses;
            var total_earnings = [];
            var total_expenses = [];

            for (var i = 0; i <= 11; i++) {
                var monthName = getMonthNameFromIndex(i); // Implement this function to get month name

                var earningsData = earnings.find(item => item.month === monthName);
                total_earnings[i] = earningsData ? earningsData.total : 0;

                var expensesData = expenses.find(item => item.month === monthName);
                total_expenses[i] = expensesData ? expensesData.total : 0;
            }
            totalEarningExpenseChart(total_earnings, total_expenses)
        },
    });
}

let statiSticsValu = false;

function totalEarningExpenseChart(total_earnings, total_expenses) {
    if (statiSticsValu) {
        statiSticsValu.destroy();
    }

    var ctx = document.getElementById('monthly-statistics').getContext('2d');
    var gradient = ctx.createLinearGradient(0, 100, 10, 280);
    gradient.addColorStop(0, '#292BE9');
    gradient.addColorStop(1, 'rgba(50, 130, 241, 0)');

    var gradient2 = ctx.createLinearGradient(0, 0, 0, 290);
    gradient2.addColorStop(0, 'rgba(255, 68, 5, 1)');
    gradient2.addColorStop(1, 'rgba(255, 68, 5, 0)');


    statiSticsValu = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'March', 'April', 'May', 'June', 'July', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                    backgroundColor: gradient,
                    label: "Income",
                    fill: true,
                    borderWidth: 1,
                    borderColor: "#292BE9",
                    data: total_earnings,
                },
                {
                    backgroundColor: gradient2,
                    label: "Expense",
                    fill: true,
                    borderWidth: 1,
                    borderColor: "rgba(255, 68, 5, 1)",
                    data: total_expenses,
                }
            ]
        },

        options: {
            responsive: true,
            tension: 0.3,
            tooltips: {
                displayColors: true,
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 30
                    }
                },
                title: {
                    // display: true,
                }
            },
            scales: {
                x: {
                    display: true,
                },
                y: {
                    display: true,
                    beginAtZero: true
                }
            },
        },
    });
};

/** dashboard end */
