@extends('layouts.master', [
    'title' => __('Edit Salary')
])

@section('main_content')
<div class="erp-table-section">
    <div class="container-fluid">
        <div class="table-header">
            <h4>{{ __('Edit Salary') }}</h4>
            <a href="{{ route('salaries.index') }}" class="add-order-btn rounded-2">
                <i class="fas fa-arrow-left"></i> {{ __('Back to Salary List') }}
            </a>
        </div>

        <div class="order-form-section">
            <form action="{{ route('salaries.update', $salary) }}" method="post" class="ajaxform">
                @csrf
                @method('PUT')
                <div class="row">
                    <!-- Employee Information (Read-only) -->
                    <div class="col-lg-12 mt-1">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6>{{ __('Employee Information') }}</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <p><strong>{{ __('Employee') }}:</strong> {{ $salary->employee->name }}</p>
                                    </div>
                                    <div class="col-md-3">
                                        <p><strong>{{ __('Employee ID') }}:</strong> {{ $salary->employee->employee_id }}</p>
                                    </div>
                                    <div class="col-md-3">
                                        <p><strong>{{ __('Period') }}:</strong> {{ $salary->salary_period }}</p>
                                    </div>
                                    <div class="col-md-3">
                                        <p><strong>{{ __('Base Salary') }}:</strong> {{ currency_format($salary->base_salary) }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Attendance and Overtime -->
                    <div class="col-lg-4 mt-3">
                        <label>{{ __('Attendance Days') }} <span class="text-danger">*</span></label>
                        <input type="number" name="attendance_days" id="attendance_days" class="form-control"
                               min="0" max="26" value="{{ $salary->attendance_days }}" required>
                        <small class="form-text text-muted">{{ __('Maximum 26 working days per month') }}</small>
                    </div>

                    <div class="col-lg-4 mt-3">
                        <label>{{ __('Overtime Hours') }} <span class="text-danger">*</span></label>
                        <input type="number" name="overtime_hours" id="overtime_hours" class="form-control"
                               min="0" step="0.5" value="{{ $salary->overtime_hours }}" required>
                        <small class="form-text text-muted">{{ __('Enter overtime hours worked') }}</small>
                    </div>

                    <div class="col-lg-4 mt-3">
                        <label>{{ __('Attendance Bonus') }}</label>
                        <input type="number" name="attendance_bonus" id="attendance_bonus" class="form-control"
                               min="0" step="0.01" value="{{ $salary->attendance_bonus ?? 0 }}">
                        <small class="form-text text-muted">{{ __('Additional bonus amount') }}</small>
                    </div>

                    <!-- Salary Calculation Display -->
                    <div class="col-lg-12 mt-3" id="salary-calculation">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h6>{{ __('Salary Calculation') }}</h6>
                                <div class="row">
                                    <div class="col-md-2">
                                        <p><strong>{{ __('Base Salary') }}:</strong><br><span id="calc-base-salary">{{ currency_format($salary->base_salary) }}</span></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p><strong>{{ __('Attendance Salary') }}:</strong><br><span id="calc-attendance-salary">0</span></p>
                                    </div>
                                    <div class="col-md-2">
                                        <p><strong>{{ __('Overtime Salary') }}:</strong><br><span id="calc-overtime-salary">0</span></p>
                                    </div>
                                    <div class="col-md-2">
                                        <p><strong>{{ __('Bonus') }}:</strong><br><span id="calc-bonus">{{ currency_format($salary->attendance_bonus ?? 0) }}</span></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p><strong>{{ __('Total Calculated') }}:</strong><br><span id="calc-total-salary">{{ currency_format($salary->calculated_salary) }}</span></p>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small>{{ __('Formula: (Base Salary × Attendance Days ÷ 26) + (Base Salary ÷ 1000 × 5 × Overtime Hours)') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bank Selection -->
                    <div class="col-lg-6 mt-3">
                        <label>{{ __('Payment Bank') }}</label>
                        <select name="bank_id" id="bank_id" class="form-control">
                            <option value="">{{ __('Cash Payment') }}</option>
                            @foreach($banks as $bank)
                                <option value="{{ $bank->id }}" {{ $salary->bank_id == $bank->id ? 'selected' : '' }}>
                                    {{ $bank->bank_name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Notes -->
                    <div class="col-lg-6 mt-3">
                        <label>{{ __('Notes') }}</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3" 
                                  placeholder="{{ __('Additional notes about this salary') }}">{{ $salary->notes }}</textarea>
                    </div>

                    <!-- Remarks -->
                    <div class="col-lg-12 mt-3">
                        <label>{{ __('Remarks') }}</label>
                        <textarea name="remarks" id="remarks" class="form-control" rows="2" 
                                  placeholder="{{ __('Internal remarks or comments') }}">{{ $salary->remarks }}</textarea>
                    </div>

                    <!-- Current Advance Information -->
                    @if($salary->advance_amount > 0)
                    <div class="col-lg-12 mt-3">
                        <div class="card bg-warning">
                            <div class="card-body">
                                <h6>{{ __('Advance Information') }}</h6>
                                <p>{{ __('This employee has received an advance of') }} <strong>{{ currency_format($salary->advance_amount) }}</strong> {{ __('for this period.') }}</p>
                                <p>{{ __('Final salary will be:') }} <strong>{{ currency_format($salary->calculated_salary - $salary->advance_amount) }}</strong></p>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Submit Button -->
                    <div class="col-lg-12 mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> {{ __('Update Salary') }}
                        </button>
                        <a href="{{ route('salaries.show', $salary) }}" class="btn btn-info">
                            <i class="fas fa-eye"></i> {{ __('View Details') }}
                        </a>
                        <a href="{{ route('salaries.index') }}" class="btn btn-secondary">
                            {{ __('Cancel') }}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('js')
<script>
$(document).ready(function() {
    const baseSalary = parseFloat('{{ $salary->base_salary ?? 0 }}');

    // Calculate salary on page load
    calculateSalary();

    // Attendance days, overtime hours, and bonus change
    $('#attendance_days, #overtime_hours, #attendance_bonus').on('input', function() {
        calculateSalary();
    });

    function calculateSalary() {
        const attendanceDays = parseInt($('#attendance_days').val()) || 0;
        const overtimeHours = parseFloat($('#overtime_hours').val()) || 0;
        const attendanceBonus = parseFloat($('#attendance_bonus').val()) || 0;

        if (baseSalary > 0) {
            // Calculate using the formula
            const attendanceSalary = (baseSalary * attendanceDays) / 26;
            const overtimeSalary = (baseSalary / 1000) * 5 * overtimeHours;
            const totalSalary = attendanceSalary + overtimeSalary + attendanceBonus;

            // Display calculations
            $('#calc-base-salary').text(formatCurrency(baseSalary));
            $('#calc-attendance-salary').text(formatCurrency(attendanceSalary));
            $('#calc-overtime-salary').text(formatCurrency(overtimeSalary));
            $('#calc-bonus').text(formatCurrency(attendanceBonus));
            $('#calc-total-salary').text(formatCurrency(totalSalary));
        }
    }

    function formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount);
    }
});
</script>
@endpush
