// 1. Migration for employees table
Schema::create('employees', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->decimal('monthly_salary', 10, 2);
    $table->timestamps();
});

// 2. Migration for attendances table
Schema::create('attendances', function (Blueprint $table) {
    $table->id();
    $table->foreignId('employee_id')->constrained()->onDelete('cascade');
    $table->date('attendance_date');
    $table->enum('status', ['present', 'absent', 'half_day']);
    $table->decimal('daily_cost', 10, 2);
    $table->timestamps();
    $table->unique(['employee_id', 'attendance_date']);
});

// 3. Employee model
class Employee extends Model {
    protected $fillable = ['name', 'monthly_salary'];
    public function attendances() {
        return $this->hasMany(Attendance::class);
    }
}

// 4. Attendance model
class Attendance extends Model {
    protected $fillable = ['employee_id', 'attendance_date', 'status', 'daily_cost'];
    public function employee() {
        return $this->belongsTo(Employee::class);
    }
}

// 5. AttendanceController.php
class AttendanceController extends Controller
{
    public function create(Request $request)
    {
        $date = $request->query('date') ?? date('Y-m-d');
        $employees = Employee::all();

        return view('attendance.create', compact('employees', 'date'));
    }

    public function store(Request $request)
    {
        $date = $request->input('attendance_date');
        foreach ($request->input('attendances') as $employeeId => $status) {
            $employee = Employee::findOrFail($employeeId);
            $dailyRate = $employee->monthly_salary / 30;
            $cost = 0;

            if ($status === 'present') $cost = $dailyRate;
            elseif ($status === 'half_day') $cost = $dailyRate / 2;

            Attendance::updateOrCreate(
                [ 'employee_id' => $employeeId, 'attendance_date' => $date ],
                [ 'status' => $status, 'daily_cost' => $cost ]
            );
        }

        return redirect()->route('attendance.create', ['date' => $date])->with('success', 'Attendance saved.');
    }

    public function summary(Request $request)
    {
        $date = $request->query('date') ?? date('Y-m-d');
        $attendances = Attendance::with('employee')->where('attendance_date', $date)->get();
        $totalCost = $attendances->sum('daily_cost');

        return view('attendance.summary', compact('attendances', 'date', 'totalCost'));
    }
}

// 6. web.php (Routes)
Route::get('/attendance/create', [AttendanceController::class, 'create'])->name('attendance.create');
Route::post('/attendance/store', [AttendanceController::class, 'store'])->name('attendance.store');
Route::get('/attendance/summary', [AttendanceController::class, 'summary'])->name('attendance.summary');

// 7. Blade Template: resources/views/attendance/create.blade.php
@extends('layouts.app')
@section('content')
<div class="container">
    <h4 class="mb-3">Daily Attendance for {{ $date }}</h4>
    <form method="POST" action="{{ route('attendance.store') }}">
        @csrf
        <input type="hidden" name="attendance_date" value="{{ $date }}">
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>Employee Name</th>
                    <th>Monthly Salary</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                @foreach($employees as $employee)
                    <tr>
                        <td>{{ $employee->name }}</td>
                        <td>{{ number_format($employee->monthly_salary, 2) }}</td>
                        <td>
                            <select name="attendances[{{ $employee->id }}]" class="form-control">
                                <option value="present">Present</option>
                                <option value="absent">Absent</option>
                                <option value="half_day">Half Day</option>
                            </select>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
        <button type="submit" class="btn btn-primary">Save Attendance</button>
    </form>
</div>
@endsection

// 8. Blade Template: resources/views/attendance/summary.blade.php
@extends('layouts.app')
@section('content')
<div class="container">
    <h4 class="mb-3">Attendance Summary for {{ $date }}</h4>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Employee Name</th>
                <th>Status</th>
                <th>Daily Cost</th>
            </tr>
        </thead>
        <tbody>
            @foreach($attendances as $attendance)
                <tr>
                    <td>{{ $attendance->employee->name }}</td>
                    <td>{{ ucfirst($attendance->status) }}</td>
                    <td>{{ number_format($attendance->daily_cost, 2) }}</td>
                </tr>
            @endforeach
        </tbody>
        <tfoot>
            <tr>
                <th colspan="2" class="text-right">Total Cost</th>
                <th>{{ number_format($totalCost, 2) }}</th>
            </tr>
        </tfoot>
    </table>
</div>
@endsection
