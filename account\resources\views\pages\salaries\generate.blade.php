@extends('layouts.master', [
    'title' => __('Generate Salary')
])

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
.select2-container {
    width: 100% !important;
}
.select2-container--default .select2-selection--single {
    height: 40px;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    background-color: #fff;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 38px;
    padding-left: 12px;
    color: #495057;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 38px;
    right: 10px;
}
.select2-container--default .select2-selection--single:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.select2-dropdown {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #007bff;
}
.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 8px 12px;
}
</style>
@endpush

@section('main_content')
<div class="erp-table-section">
    <div class="container-fluid">
        <div class="table-header">
            <h4>{{ __('Generate Salary') }}</h4>
            <a href="{{ route('salaries.index') }}" class="add-order-btn rounded-2">
                <i class="fas fa-arrow-left"></i> {{ __('Back to Salary List') }}
            </a>
        </div>

        <div class="order-form-section">
            <form action="{{ route('salaries.store') }}" method="post" class="ajaxform">
                @csrf
                <div class="row">
                    <!-- Employee Selection -->
                    <div class="col-lg-6 mt-1">
                        <label>{{ __('Employee') }} <span class="text-danger">*</span></label>
                        <select name="employee_id" id="employee_id" class="form-control select2" required>
                            <option value="">{{ __('Select Employee') }}</option>
                            @foreach($employees as $employee)
                                <option value="{{ $employee->id }}" data-salary="{{ $employee->salary }}"
                                        data-employee-id="{{ $employee->employee_id }}">
                                    {{ $employee->employee_id }} - {{ $employee->name }} ({{ currency_format($employee->salary) }})
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Salary Period -->
                    <div class="col-lg-3 mt-1">
                        <label>{{ __('Year') }} <span class="text-danger">*</span></label>
                        <select name="year" id="year" class="form-control" required>
                            @for($year = $currentYear; $year >= $currentYear - 2; $year--)
                                <option value="{{ $year }}" {{ $year == $currentYear ? 'selected' : '' }}>
                                    {{ $year }}
                                </option>
                            @endfor
                        </select>
                    </div>

                    <div class="col-lg-3 mt-1">
                        <label>{{ __('Month') }} <span class="text-danger">*</span></label>
                        <select name="month" id="month" class="form-control" required>
                            @for($month = 1; $month <= 12; $month++)
                                <option value="{{ $month }}" {{ $month == $currentMonth ? 'selected' : '' }}>
                                    {{ \Carbon\Carbon::create()->month($month)->format('F') }}
                                </option>
                            @endfor
                        </select>
                    </div>

                    <!-- Employee Info Display -->
                    <div class="col-lg-12 mt-3" id="employee-info" style="display: none;">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6>{{ __('Employee Information') }}</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <p><strong>{{ __('Employee ID') }}:</strong> <span id="display-employee-id">-</span></p>
                                    </div>
                                    <div class="col-md-4">
                                        <p><strong>{{ __('Base Salary') }}:</strong> <span id="display-base-salary">-</span></p>
                                    </div>
                                    <div class="col-md-4">
                                        <p><strong>{{ __('Join Date') }}:</strong> <span id="display-join-date">-</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Attendance and Overtime -->
                    <div class="col-lg-4 mt-3">
                        <label>{{ __('Attendance Days') }} <span class="text-danger">*</span></label>
                        <input type="number" name="attendance_days" id="attendance_days" class="form-control"
                               min="0" max="26" value="26" required>
                        <small class="form-text text-muted">{{ __('Maximum 26 working days per month') }}</small>
                    </div>

                    <div class="col-lg-4 mt-3">
                        <label>{{ __('Overtime Hours') }} <span class="text-danger">*</span></label>
                        <input type="number" name="overtime_hours" id="overtime_hours" class="form-control"
                               min="0" step="0.5" value="0" required>
                        <small class="form-text text-muted">{{ __('Enter overtime hours worked') }}</small>
                    </div>

                    <div class="col-lg-4 mt-3">
                        <label>{{ __('Attendance Bonus') }}</label>
                        <input type="number" name="attendance_bonus" id="attendance_bonus" class="form-control"
                               min="0" step="0.01" value="0">
                        <small class="form-text text-muted">{{ __('Additional bonus amount') }}</small>
                    </div>

                    <!-- Salary Calculation Display -->
                    <div class="col-lg-12 mt-3" id="salary-calculation" style="display: none;">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h6>{{ __('Salary Calculation') }}</h6>
                                <div class="row">
                                    <div class="col-md-2">
                                        <p><strong>{{ __('Base Salary') }}:</strong><br><span id="calc-base-salary">0</span></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p><strong>{{ __('Attendance Salary') }}:</strong><br><span id="calc-attendance-salary">0</span></p>
                                    </div>
                                    <div class="col-md-2">
                                        <p><strong>{{ __('Overtime Salary') }}:</strong><br><span id="calc-overtime-salary">0</span></p>
                                    </div>
                                    <div class="col-md-2">
                                        <p><strong>{{ __('Bonus') }}:</strong><br><span id="calc-bonus">0</span></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p><strong>{{ __('Total Calculated') }}:</strong><br><span id="calc-total-salary">0</span></p>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small>{{ __('Formula: (Base Salary × Attendance Days ÷ 26) + (Base Salary ÷ 1000 × 5 × Overtime Hours)') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bank Selection -->
                    <div class="col-lg-6 mt-3">
                        <label>{{ __('Payment Bank') }}</label>
                        <select name="bank_id" id="bank_id" class="form-control">
                            <option value="">{{ __('Cash Payment') }}</option>
                            @foreach($banks as $bank)
                                <option value="{{ $bank->id }}">{{ $bank->bank_name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Notes -->
                    <div class="col-lg-6 mt-3">
                        <label>{{ __('Notes') }}</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3" 
                                  placeholder="{{ __('Additional notes about this salary') }}"></textarea>
                    </div>

                    <!-- Remarks -->
                    <div class="col-lg-12 mt-3">
                        <label>{{ __('Remarks') }}</label>
                        <textarea name="remarks" id="remarks" class="form-control" rows="2" 
                                  placeholder="{{ __('Internal remarks or comments') }}"></textarea>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-lg-12 mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> {{ __('Generate Salary') }}
                        </button>
                        <a href="{{ route('salaries.index') }}" class="btn btn-secondary">
                            {{ __('Cancel') }}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('js')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize Select2 for employee dropdown
    // $('#employee_id').select2({
    //     placeholder: '{{ __("Search and select employee...") }}',
    //     allowClear: true,
    //     width: '100%',
    //     dropdownAutoWidth: true,
    //     minimumInputLength: 0,
    //     templateResult: formatEmployee,
    //     templateSelection: formatEmployeeSelection,
    //     escapeMarkup: function(markup) {
    //         return markup;
    //     }
    // });

    // Custom formatting for employee options
    function formatEmployee(employee) {
        if (employee.loading) {
            return employee.text;
        }

        if (!employee.id) {
            return employee.text;
        }

        var $employee = $(
            '<div class="select2-employee-option">' +
                '<div class="employee-name">' + employee.text + '</div>' +
            '</div>'
        );

        return $employee;
    }

    // Custom formatting for selected employee
    function formatEmployeeSelection(employee) {
        return employee.text || employee.id;
    }

    // Employee selection change
    $('#employee_id').change(function() {
        const employeeId = $(this).val();
        const selectedOption = $(this).find('option:selected');
        
        if (employeeId) {
            const baseSalary = selectedOption.data('salary');
            const employeeIdText = selectedOption.data('employee-id');
            
            // Show employee info
            $('#employee-info').show();
            $('#display-employee-id').text(employeeIdText);
            $('#display-base-salary').text(formatCurrency(baseSalary));
            
            // Get additional employee info via AJAX
            $.ajax({
                url: '{{ route("salaries.employee-info") }}',
                method: 'GET',
                data: { employee_id: employeeId },
                success: function(response) {
                    $('#display-join-date').text(response.join_date);
                },
                error: function() {
                    $('#display-join-date').text('-');
                }
            });
            
            calculateSalary();
        } else {
            $('#employee-info').hide();
            $('#salary-calculation').hide();
        }
    });

    // Attendance days, overtime hours, and bonus change
    $('#attendance_days, #overtime_hours, #attendance_bonus').on('input', function() {
        calculateSalary();
    });

    function calculateSalary() {
        const employeeId = $('#employee_id').val();
        const selectedOption = $('#employee_id').find('option:selected');
        const baseSalary = parseFloat(selectedOption.data('salary')) || 0;
        const attendanceDays = parseInt($('#attendance_days').val()) || 0;
        const overtimeHours = parseFloat($('#overtime_hours').val()) || 0;
        const attendanceBonus = parseFloat($('#attendance_bonus').val()) || 0;

        if (employeeId && baseSalary > 0) {
            // Calculate using the formula
            const attendanceSalary = (baseSalary * attendanceDays) / 26;
            const overtimeSalary = (baseSalary / 1000) * 5 * overtimeHours;
            const totalSalary = attendanceSalary + overtimeSalary + attendanceBonus;

            // Display calculations
            $('#calc-base-salary').text(formatCurrency(baseSalary));
            $('#calc-attendance-salary').text(formatCurrency(attendanceSalary));
            $('#calc-overtime-salary').text(formatCurrency(overtimeSalary));
            $('#calc-bonus').text(formatCurrency(attendanceBonus));
            $('#calc-total-salary').text(formatCurrency(totalSalary));

            $('#salary-calculation').show();
        } else {
            $('#salary-calculation').hide();
        }
    }

    function formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount);
    }
});
</script>
@endpush
