{"compilerOptions": {"allowJs": true, "module": "ESNext", "moduleResolution": "bundler", "jsx": "preserve", "strict": true, "isolatedModules": true, "target": "ESNext", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "skipLibCheck": true, "paths": {"@/*": ["./resources/js/*"], "ziggy-js": ["./vendor/tightenco/ziggy"]}}, "include": ["resources/js/**/*.ts", "resources/js/**/*.d.ts", "resources/js/**/*.vue"]}