@extends('layouts.master', [
    'title' => __('Salary Management')
])

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
.select2-container {
    width: 100% !important;
}
.select2-container--default .select2-selection--single {
    height: 38px;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 36px;
    padding-left: 12px;
}
</style>
@endpush

@section('main_content')
<div class="erp-table-section">
    <div class="container-fluid">
        <div class="table-header">
            <h4>{{ __('Salary Management') }}</h4>
            <div class="header-actions">
                @can('salaries-create')
                    <a href="{{ route('salaries.generate') }}" class="add-order-btn rounded-2">
                        <i class="fas fa-plus-circle"></i> {{ __('Generate Salary') }}
                    </a>
                    <a href="{{ route('advance-salaries.create') }}" class="add-order-btn rounded-2 btn-warning">
                        <i class="fas fa-hand-holding-usd"></i> {{ __('Advance Salary') }}
                    </a>
                @endcan
            </div>
        </div>

        <!-- Filter Section -->
        <div class="table-top-form daily-transaction-between-wrapper mb-4">
            <form method="GET" action="{{ route('salaries.index') }}" class="filter-form">
                <div class="row">
                    <div class="col-md-3">
                        <div class="input-wrapper">
                            <label for="employee_id">{{ __('Employee') }}</label>
                            <select name="employee_id" id="employee_id" class="form-control select2-filter">
                                <option value="">{{ __('All Employees') }}</option>
                                @foreach($employees as $employee)
                                    <option value="{{ $employee->id }}" {{ request('employee_id') == $employee->id ? 'selected' : '' }}>
                                        {{ $employee->employee_id }} - {{ $employee->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="input-wrapper">
                            <label for="year">{{ __('Year') }}</label>
                            <select name="year" id="year" class="form-control">
                                <option value="">{{ __('All Years') }}</option>
                                @foreach($years as $year)
                                    <option value="{{ $year }}" {{ request('year') == $year ? 'selected' : '' }}>
                                        {{ $year }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="input-wrapper">
                            <label for="month">{{ __('Month') }}</label>
                            <select name="month" id="month" class="form-control">
                                <option value="">{{ __('All Months') }}</option>
                                @foreach($months as $month)
                                    <option value="{{ $month['value'] }}" {{ request('month') == $month['value'] ? 'selected' : '' }}>
                                        {{ $month['name'] }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="input-wrapper">
                            <label for="status">{{ __('Status') }}</label>
                            <select name="status" id="status" class="form-control">
                                <option value="">{{ __('All Status') }}</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>{{ __('Pending') }}</option>
                                <option value="paid" {{ request('status') == 'paid' ? 'selected' : '' }}>{{ __('Paid') }}</option>
                                <option value="advance" {{ request('status') == 'advance' ? 'selected' : '' }}>{{ __('Advance') }}</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-wrapper">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> {{ __('Filter') }}
                                </button>
                                <a href="{{ route('salaries.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> {{ __('Clear') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Salary Table -->
        <div class="responsive-table">
            <table class="table" id="erp-table">
                <thead>
                    <tr>
                        <th>{{ __('S/N') }}</th>
                        <th>{{ __('Employee') }}</th>
                        <th>{{ __('Period') }}</th>
                        <th>{{ __('Base Salary') }}</th>
                        <th>{{ __('Attendance') }}</th>
                        <th>{{ __('Overtime') }}</th>
                        <th>{{ __('Bonus') }}</th>
                        <th>{{ __('Calculated') }}</th>
                        <th>{{ __('Advance') }}</th>
                        <th>{{ __('Final Amount') }}</th>
                        <th>{{ __('Status') }}</th>
                        <th>{{ __('Payment Date') }}</th>
                        <th class="print-d-none">{{ __('Actions') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($salaries as $salary)
                        <tr>
                            <td>{{ $loop->iteration }}</td>
                            <td>
                                <div>
                                    <strong>{{ $salary->employee->name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $salary->employee->employee_id }}</small>
                                </div>
                            </td>
                            <td>{{ $salary->salary_period }}</td>
                            <td>{{ currency_format($salary->base_salary) }}</td>
                            <td>{{ $salary->attendance_days }} days</td>
                            <td>{{ $salary->overtime_hours }} hrs</td>
                            <td>{{ currency_format($salary->attendance_bonus ?? 0) }}</td>
                            <td>{{ currency_format($salary->calculated_salary) }}</td>
                            <td>{{ currency_format($salary->advance_amount) }}</td>
                            <td>
                                <strong class="text-success">{{ currency_format($salary->final_salary) }}</strong>
                            </td>
                            <td>
                                @if($salary->payment_status === 'pending')
                                    <span class="badge bg-warning">{{ __('Pending') }}</span>
                                @elseif($salary->payment_status === 'paid')
                                    <span class="badge bg-success">{{ __('Paid') }}</span>
                                @elseif($salary->payment_status === 'advance')
                                    <span class="badge bg-info">{{ __('Advance') }}</span>
                                @endif
                            </td>
                            <td>
                                {{ $salary->payment_date ? $salary->payment_date->format('M d, Y') : '-' }}
                            </td>
                            <td class="print-d-none">
                                <div class="dropdown table-action">
                                    <button type="button" data-bs-toggle="dropdown" class="btn btn-sm">
                                        <i class="far fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a href="{{ route('salaries.show', $salary) }}" class="dropdown-item">
                                                <i class="far fa-eye text-info"></i> {{ __('View Details') }}
                                            </a>
                                        </li>
                                        @can('salaries-update')
                                            @if($salary->payment_status === 'pending')
                                                <li>
                                                    <a href="{{ route('salaries.edit', $salary) }}" class="dropdown-item">
                                                        <i class="far fa-edit text-primary"></i> {{ __('Edit') }}
                                                    </a>
                                                </li>
                                                <li>
                                                    <button type="button" class="dropdown-item mark-paid-btn"
                                                            data-salary-id="{{ $salary->id }}">
                                                        <i class="far fa-check-circle text-success"></i> {{ __('Mark as Paid') }}
                                                    </button>
                                                </li>
                                            @endif
                                        @endcan
                                        @can('salaries-delete')
                                            @if($salary->payment_status !== 'paid')
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <button type="button" class="dropdown-item text-danger delete-btn"
                                                            data-action="{{ route('salaries.destroy', $salary) }}">
                                                        <i class="far fa-trash-alt text-danger"></i> {{ __('Delete') }}
                                                    </button>
                                                </li>
                                            @endif
                                        @endcan
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="13" class="text-center">{{ __('No salary records found') }}</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>

            <!-- Pagination -->
            @if($salaries->hasPages())
                <nav>
                    <ul class="pagination">
                        <li class="page-item">{{ $salaries->appends(request()->query())->links() }}</li>
                    </ul>
                </nav>
            @endif
        </div>
    </div>
</div>
<!-- Mark as Paid Modal -->
<div class="modal fade" id="markPaidModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Mark Salary as Paid') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="markPaidForm" class="ajaxform">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="payment_date">{{ __('Payment Date') }} <span class="text-danger">*</span></label>
                                <input type="date" name="payment_date" id="payment_date" class="form-control"
                                       value="{{ date('Y-m-d') }}" max="{{ date('Y-m-d') }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="bank_id">{{ __('Bank') }}</label>
                                <select name="bank_id" id="bank_id" class="form-control">
                                    <option value="">{{ __('Cash Payment') }}</option>
                                    @foreach(\App\Models\Bank::orderBy('bank_name')->get() as $bank)
                                        <option value="{{ $bank->id }}">{{ $bank->bank_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="cheque_no">{{ __('Cheque Number') }}</label>
                        <input type="text" name="cheque_no" id="cheque_no" class="form-control"
                               placeholder="{{ __('Enter cheque number if applicable') }}">
                    </div>
                    <div class="form-group">
                        <label for="payment_remarks">{{ __('Payment Remarks') }}</label>
                        <textarea name="remarks" id="payment_remarks" class="form-control" rows="3"
                                  placeholder="{{ __('Additional payment notes') }}"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-success">{{ __('Mark as Paid') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('js')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize Select2 for employee filter
    $('.select2-filter').select2({
        placeholder: '{{ __("Search employees...") }}',
        allowClear: true,
        width: '100%'
    });

    // Delete functionality
    $(document).on('click', '.delete-btn', function() {
        const action = $(this).data('action');
        
        $.confirm({
            title: '{{ __("Are you sure?") }}',
            content: '{{ __("This action cannot be undone. The salary record will be permanently deleted.") }}',
            icon: 'fas fa-exclamation-triangle',
            theme: 'modern',
            closeIcon: true,
            animation: 'scale',
            type: 'red',
            buttons: {
                confirm: {
                    btnClass: 'btn-red',
                    text: '{{ __("Yes, Delete") }}',
                    action: function() {
                        $.ajax({
                            url: action,
                            method: 'DELETE',
                            data: {
                                _token: '{{ csrf_token() }}'
                            },
                            success: function(response) {
                                if (response.message) {
                                    toastr.success(response.message);
                                }
                                if (response.redirect) {
                                    window.location.href = response.redirect;
                                } else {
                                    location.reload();
                                }
                            },
                            error: function(xhr) {
                                const response = xhr.responseJSON;
                                if (response && response.message) {
                                    toastr.error(response.message);
                                } else {
                                    toastr.error('{{ __("An error occurred while deleting the salary record") }}');
                                }
                            }
                        });
                    }
                },
                cancel: {
                    text: '{{ __("Cancel") }}',
                    action: function() {
                        // Do nothing
                    }
                }
            }
        });
    });

    // Mark as Paid functionality
    $(document).on('click', '.mark-paid-btn', function() {
        const salaryId = $(this).data('salary-id');
        const form = $('#markPaidForm');
        form.attr('action', `/salaries/${salaryId}/mark-paid`);
        $('#markPaidModal').modal('show');
    });

    // Handle mark as paid form submission
    $('#markPaidForm').on('submit', function(e) {
        e.preventDefault();

        const form = $(this);
        const url = form.attr('action');
        const formData = form.serialize();

        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            success: function(response) {
                $('#markPaidModal').modal('hide');
                if (response.message) {
                    toastr.success(response.message);
                }
                if (response.redirect) {
                    window.location.href = response.redirect;
                } else {
                    location.reload();
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                if (response && response.message) {
                    toastr.error(response.message);
                } else {
                    toastr.error('{{ __("An error occurred") }}');
                }
            }
        });
    });
});
</script>
@endpush