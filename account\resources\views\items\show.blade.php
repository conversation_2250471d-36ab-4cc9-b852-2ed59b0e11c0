@extends('layouts.master')

@section('main_content')
    <div style="padding: 40px 20px; background-color: #f4f5f7; min-height: 100vh; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; color: #2c3e50;">
        <div style="max-width: 900px; margin: 0 auto; background: #fff; border-radius: 16px; box-shadow: 0 4px 16px rgba(44, 62, 80, 0.1); padding: 32px;">
            <h1 style="font-weight: 700; font-size: 28px; margin-bottom: 24px; color: #1e293b;">Item Details</h1>
            
            <div style="margin-bottom: 32px; font-size: 16px; line-height: 1.5; color: #475569;">
                <p><strong style="color: #334155;">ID:</strong> {{ $item->id }}</p>
                <p><strong style="color: #334155;">Date:</strong> {{ \Carbon\Carbon::parse($item->date)->format('d M Y') }}</p>
            </div>

            <h2 style="font-weight: 600; font-size: 22px; margin-bottom: 16px; color: #1e293b;">Item Sizes</h2>
            <div style="overflow-x:auto;">
                <table style="width: 100%; border-collapse: collapse; box-shadow: inset 0 -1px 0 #e2e8f0;">
                    <thead>
                        <tr style="background-color: #f1f5f9; color: #64748b; text-align: left;">
                            <th style="padding: 12px 16px; font-weight: 600; border-bottom: 2px solid #cbd5e1;">Part</th>
                            <th style="padding: 12px 16px; font-weight: 600; border-bottom: 2px solid #cbd5e1;">Qty</th>
                            <th style="padding: 12px 16px; font-weight: 600; border-bottom: 2px solid #cbd5e1;">Color</th>
                            <th style="padding: 12px 16px; font-weight: 600; border-bottom: 2px solid #cbd5e1;">Size</th>
                            <th style="padding: 12px 16px; font-weight: 600; border-bottom: 2px solid #cbd5e1;">Total</th>
                            <th style="padding: 12px 16px; font-weight: 600; border-bottom: 2px solid #cbd5e1;">Type</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($item->itemSizes as $itemSize)
                            <tr style="border-bottom: 1px solid #e2e8f0; color: #334155;">
                                <td style="padding: 12px 16px;">{{ ucfirst($itemSize->part) }}</td>
                                <td style="padding: 12px 16px;">{{ $itemSize->qty }}</td>
                                <td style="padding: 12px 16px;">{{ ucfirst($itemSize->color) }}</td>
                                <td style="padding: 12px 16px;">{{ $itemSize->size }}</td>
                                <td style="padding: 12px 16px;">{{ number_format($itemSize->total, 2) }}</td>
                                <td style="padding: 12px 16px;">{{ ucfirst($itemSize->type) }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <a href="{{ route('items.index') }}" 
               style="display: inline-block; margin-top: 32px; padding: 12px 28px; background-color: #3b82f6; color: white; font-weight: 600; border-radius: 9999px; text-decoration: none; box-shadow: 0 2px 6px rgba(59, 130, 246, 0.5); transition: background-color 0.3s ease;">
                ← Back to List
            </a>
        </div>
    </div>
@endsection
