{"auth.failed": "These credentials do not match our records.", "auth.password": "The provided password is incorrect.", "auth.throttle": "Too many login attempts. Please try again in :seconds seconds.", "pagination.previous": "&laquo; Previous", "pagination.next": "Next &raquo;", "passwords.reset": "Your password has been reset!", "passwords.sent": "We have emailed your password reset link!", "passwords.throttled": "Please wait before retrying.", "passwords.token": "This password reset token is invalid.", "passwords.user": "We can't find a user with that email address.", "validation.accepted": "The :attribute must be accepted.", "validation.accepted_if": "The :attribute must be accepted when :other is :value.", "validation.active_url": "The :attribute is not a valid URL.", "validation.after": "The :attribute must be a date after :date.", "validation.after_or_equal": "The :attribute must be a date after or equal to :date.", "validation.alpha": "The :attribute must only contain letters.", "validation.alpha_dash": "The :attribute must only contain letters, numbers, dashes and underscores.", "validation.alpha_num": "The :attribute must only contain letters and numbers.", "validation.array": "The :attribute must be an array.", "validation.before": "The :attribute must be a date before :date.", "validation.before_or_equal": "The :attribute must be a date before or equal to :date.", "validation.between.array": "The :attribute must have between :min and :max items.", "validation.between.file": "The :attribute must be between :min and :max kilobytes.", "validation.between.numeric": "The :attribute must be between :min and :max.", "validation.between.string": "The :attribute must be between :min and :max characters.", "validation.boolean": "The :attribute field must be true or false.", "validation.confirmed": "The :attribute confirmation does not match.", "validation.current_password": "The password is incorrect.", "validation.date": "The :attribute is not a valid date.", "validation.date_equals": "The :attribute must be a date equal to :date.", "validation.date_format": "The :attribute does not match the format :format.", "validation.declined": "The :attribute must be declined.", "validation.declined_if": "The :attribute must be declined when :other is :value.", "validation.different": "The :attribute and :other must be different.", "validation.digits": "The :attribute must be :digits digits.", "validation.digits_between": "The :attribute must be between :min and :max digits.", "validation.dimensions": "The :attribute has invalid image dimensions.", "validation.distinct": "The :attribute field has a duplicate value.", "validation.email": "The :attribute must be a valid email address.", "validation.ends_with": "The :attribute must end with one of the following: :values.", "validation.enum": "The selected :attribute is invalid.", "validation.exists": "The selected :attribute is invalid.", "validation.file": "The :attribute must be a file.", "validation.filled": "The :attribute field must have a value.", "validation.gt.array": "The :attribute must have more than :value items.", "validation.gt.file": "The :attribute must be greater than :value kilobytes.", "validation.gt.numeric": "The :attribute must be greater than :value.", "validation.gt.string": "The :attribute must be greater than :value characters.", "validation.gte.array": "The :attribute must have :value items or more.", "validation.gte.file": "The :attribute must be greater than or equal to :value kilobytes.", "validation.gte.numeric": "The :attribute must be greater than or equal to :value.", "validation.gte.string": "The :attribute must be greater than or equal to :value characters.", "validation.image": "The :attribute must be an image.", "validation.in": "The selected :attribute is invalid.", "validation.in_array": "The :attribute field does not exist in :other.", "validation.integer": "The :attribute must be an integer.", "validation.ip": "The :attribute must be a valid IP address.", "validation.ipv4": "The :attribute must be a valid IPv4 address.", "validation.ipv6": "The :attribute must be a valid IPv6 address.", "validation.json": "The :attribute must be a valid JSON string.", "validation.lt.array": "The :attribute must have less than :value items.", "validation.lt.file": "The :attribute must be less than :value kilobytes.", "validation.lt.numeric": "The :attribute must be less than :value.", "validation.lt.string": "The :attribute must be less than :value characters.", "validation.lte.array": "The :attribute must not have more than :value items.", "validation.lte.file": "The :attribute must be less than or equal to :value kilobytes.", "validation.lte.numeric": "The :attribute must be less than or equal to :value.", "validation.lte.string": "The :attribute must be less than or equal to :value characters.", "validation.mac_address": "The :attribute must be a valid MAC address.", "validation.max.array": "The :attribute must not have more than :max items.", "validation.max.file": "The :attribute must not be greater than :max kilobytes.", "validation.max.numeric": "The :attribute must not be greater than :max.", "validation.max.string": "The :attribute must not be greater than :max characters.", "validation.mimes": "The :attribute must be a file of type: :values.", "validation.mimetypes": "The :attribute must be a file of type: :values.", "validation.min.array": "The :attribute must have at least :min items.", "validation.min.file": "The :attribute must be at least :min kilobytes.", "validation.min.numeric": "The :attribute must be at least :min.", "validation.min.string": "The :attribute must be at least :min characters.", "validation.multiple_of": "The :attribute must be a multiple of :value.", "validation.not_in": "The selected :attribute is invalid.", "validation.not_regex": "The :attribute format is invalid.", "validation.numeric": "The :attribute must be a number.", "validation.present": "The :attribute field must be present.", "validation.prohibited": "The :attribute field is prohibited.", "validation.prohibited_if": "The :attribute field is prohibited when :other is :value.", "validation.prohibited_unless": "The :attribute field is prohibited unless :other is in :values.", "validation.prohibits": "The :attribute field prohibits :other from being present.", "validation.regex": "The :attribute format is invalid.", "validation.required": "The :attribute field is required.", "validation.required_array_keys": "The :attribute field must contain entries for: :values.", "validation.required_if": "The :attribute field is required when :other is :value.", "validation.required_unless": "The :attribute field is required unless :other is in :values.", "validation.required_with": "The :attribute field is required when :values is present.", "validation.required_with_all": "The :attribute field is required when :values are present.", "validation.required_without": "The :attribute field is required when :values is not present.", "validation.required_without_all": "The :attribute field is required when none of :values are present.", "validation.same": "The :attribute and :other must match.", "validation.size.array": "The :attribute must contain :size items.", "validation.size.file": "The :attribute must be :size kilobytes.", "validation.size.numeric": "The :attribute must be :size.", "validation.size.string": "The :attribute must be :size characters.", "validation.starts_with": "The :attribute must start with one of the following: :values.", "validation.string": "The :attribute must be a string.", "validation.timezone": "The :attribute must be a valid timezone.", "validation.unique": "The :attribute has already been taken.", "validation.uploaded": "The :attribute failed to upload.", "validation.url": "The :attribute must be a valid URL.", "validation.uuid": "The :attribute must be a valid UUID.", "validation.custom.attribute-name.rule-name": "custom-message", "Dashboard": "Dashboard", "Title": "Title", "Button 1 Text": "Button 1 Text", "Button 1 Url": "Button 1 Url", "Button 2 Text": "Button 2 Text", "Button 2 Url": "Button 2 Url", "Description": "Description", "Short Title": "Short Title", "Headings": "Headings", "Welcome Section Updated": "Welcome Section Updated", "Feature Section Updated": "Feature Section Updated", "Welcome Section": "Welcome Section", "Feature Section": "Feature Section", "About Section": "About Section", "Button Url": "Button Url", "Button Text": "Button Text", "Image": "Image", "About Section Updated": "About Section Updated", "Payment Section": "Payment Section", "Payment 1 Icon": "Payment 1 Icon", "Payment 1 Text": "Payment 1 Text", "Payment 1 Description": "Payment 1 Description", "Payment 2 Icon": "Payment 2 Icon", "Payment 2 Text": "Payment 2 Text", "Payment 2 Description": "Payment 2 Description", "Payment 3 Icon": "Payment 3 Icon", "Payment 3 Text": "Payment 3 Text", "Payment 3 Description": "Payment 3 Description", "Payment 5 Icon": "Payment 5 Icon", "Payment 5 Text": "Payment 5 Text", "Payment 5 Description": "Payment 5 Description", "Payment 4 Icon": "Payment 4 Icon", "Payment 4 Text": "Payment 4 Text", "Payment 4 Description": "Payment 4 Description", "Payment 6 Icon": "Payment 6 Icon", "Payment 6 Text": "Payment 6 Text", "Payment 6 Description": "Payment 6 Description", "Payment Section Updated": "Payment Section Updated", "Integration Section": "Integration Section", "Language Name": "Language Name", "Integration code": "Integration code", "Code": "Code", "Capture Section": "Capture Section", "Integration Section Updated": "Integration Section Updated", "Capture :number Title": "Capture :number Title", "Capture :number Description": "Capture :number Description", "Security Section": "Security Section", "Security :number Description": "Security :number Description", "Security :number Icon": "Security :number Icon", "Security :number Title": "Security :number Title", "Review Section": "Review Section", "FAQ Section": "FAQ Section", "Save": "Save", "Faq Section Updated": "Faq Section Updated", "Review Section Updated": "Review Section Updated", "Latest News Section Updated": "Latest News Section Updated", "Latest News Section": "Latest News Section", "Footer Settings": "<PERSON><PERSON>s", "Copyright Content": "Copyright Content", "Footer Social Links": "Footer Social Links", "Save Changes": "Save Changes", "Website url": "Website url", "Website Url": "Website Url", "Icon Class": "Icon Class", "Footer About": "Footer About", "Footer Settings Updated": "<PERSON><PERSON> Settings Updated", "Capture Section Updated": "Capture Section Updated", "Read more": "Read more", "About Page Settings": "About <PERSON> Settings", "Currency Synced Successfully": "<PERSON><PERSON><PERSON><PERSON> Synced Successfully", "Currency Sync Failed": "<PERSON><PERSON><PERSON><PERSON> S<PERSON> Failed", "News Letter": "News Letter", "Language": "Language", "Language Not Found": "Language Not Found", "Thanks for joining our newsletter": "Thanks for joining our newsletter", "Phone": "Phone", "Email": "Email", "Our Location": "Our Location", "Map URL": "Map URL", "Contact": "Contact", "Contact Section Updated": "Contact Section Updated", "Login": "<PERSON><PERSON>", "Contact US": "Contact US", "description.contact": "Stay focused on your business.", "description.blog": "Stay focused on your business.", "description.page": "Stay focused on your business.", "Need a hand?": "Need a hand?", "Send Now": "Send Now", "Contact Mail Successfully Sent": "Contact Mail Successfully Sent", "Blog": "Blog", "Phone number": "Phone number", "Blog Posts": "Blog Posts", "No posts found!": "No posts found!", "Recent post": "Recent post", "Share Post :": "Share Post :", "Name": "Name", "Status": "Status", "Registered At": "Registered At", "Action": "Action", "Active": "Active", "Inactive": "Inactive", "Banned": "Banned", "View": "View", "Delete": "Delete", "Edit": "Edit", "Customer Not Found": "Customer Not Found", "Add Customer": "Add Customer", "Customer List": "Customer List", "Customers": "Customers", "Search user": "Search user", "Edit Customer": "Edit Customer", "Enter full name": "Enter full name", "Support Email": "Support Email", "Enter phone number": "Enter phone number", "Phone Number": "Phone Number", "Wallet Balance": "Wallet Balance", "Business Name": "Business Name", "Other Information": "Other Information", "Trading Name: :name": "Trading Name: :name", "Description: :text": "Description: :text", "Staff Size: :number": "Staff Size: :number", "Industry: :text": "Industry: :text", "Category: :text": "Category: :text", "Phone: :text": "Phone: :text", "View proof of address": "View proof of address", "Address: :text": "Address: :text", "Email: :text": "Email: :text", "Website: :text": "Website: :text", "Gender: :text": "Gender: :text", "Business Type: :text": "Business Type: :text", "Full Name: :text": "Full Name: :text", "DOB: :date": "DOB: :date", "Nationality: :text": "Nationality: :text", "ID Document: :text": "ID Document: :text", "Enter your business name": "Enter your business name", "Your full name": "Your full name", "Full Name": "Full Name", "Your email address": "Your email address", "Your phone number": "Your phone number", "Password": "Password", "Type Password": "Type Password", "Logged In Successfully": "Logged In Successfully", "agree_term_of_service_checkbox": "Agree <a href=\":url\" class=\"text-primary\">Terms & Conditions</a>", "Country": "Country", "Create Account": "Create Account", "Or Sign Up Using": "Or Sign Up Using", "Already have an account?": "Already have an account?", "Register": "Register", "You have to must agree with our terms & conditions": "You have to must agree with our terms & conditions", "Registration Successful": "Registration Successful", "Default Bank Account": "Default Bank Account", "Settlements will be paid to this account": "Settlements will be paid to this account", "Select Bank": "Select Bank", "Account Type": "Account Type", "Company": "Company", "Individual": "Individual", "Account Number": "Account Number", "Enter account number": "Enter account number", "Account Name": "Account Name", "Routing Number / Sort Code": "Routing Number / Sort Code", "Enter routing number or sort code": "Enter routing number or sort code", "Enter account name": "Enter account name", "Save Account": "Save Account", "Don't have an account?": "Don't have an account?", "Sign up": "Sign up", "Bank Account Successfully Added": "Bank Account Successfully Added", "Submit": "Submit", "Enter email": "Enter email", "Registered At: :date": "Registered At: :date", "Updated At: :date": "Updated At: :date", "IP Address: :date": "IP Address: :date", "Last Login At: :date": "Last Login At: :date", "IP Address: :ip": "IP Address: :ip", "Staff": "Staff", "Staff List": "Staff List", "Username": "Username", "Staff Not Found": "Staff Not Found", "Add Staff": "Add Staff", "Personal Information": "Personal Information", "Enter name": "Enter name", "Enter phone": "Enter phone", "Enter username": "Enter username", "Create": "Create", "Permissions": "Permissions", "Send email to all users": "Send email to all users", "Enter subject": "Enter subject", "Subject": "Subject", "Message": "Message", "Send": "Send", "Promotional Email": "Promotional Email", "Enter message": "Enter message", "No support ticket found!": "No support ticket found!", "Supports": "Supports", "Bank Transactions": "Bank Transactions", "Crypto Currency": "Crypto Currency", "Single Charge": "Single Charge", "Merchants": "Merchants", "Transaction Logs": "Transaction Logs", "Payment Plans": "Payment Plans", "Supported Banks": "Supported Banks", "Enter bank name": "Enter bank name", "Select Country": "Select Country", "Create Single Charge": "Create Single Charge", "Payment Name": "Payment Name", "Amount": "Amount", "Leave empty to allow customers enter desired amount": "Leave empty to allow customers enter desired amount", "Single Charge allows you to create payment links for your customers, Transaction Charge is :percentage per transaction": "Single Charge allows you to create payment links for your customers, Transaction Charge is :percentage per transaction", "Redirect URL": "Redirect URL", "Redirect after payment - Optional": "Redirect after payment - Optional", "Create Link": "Create Link", "Payment Title": "Payment Title", "Payment Link Created Successfully": "Payment Link Created Successfully", "No Payment Link Found": "No Payment Link Found", "We couldn't find any single charge page to this account": "We couldn't find any single charge page to this account", "Created At": "Created At", "Payment Links": "Payment Links", "Disable": "Disable", "Transactions": "Transactions", "Back": "Back", "No Transactions Found": "No Transactions Found", "Invoice No": "Invoice No", "TRX ID": "TRX ID", "Charge": "Charge", "From": "From", "Success": "Success", "Failed": "Failed", "Link": "Link", "Create Payment Link": "Create Payment Link", "Continue": "Continue", "Gateway Name": "Gateway Name", "Gateway Charge": "Gateway Charge", "Gateway Currency": "Gateway Currency", "Enter the amount you want to pay": "Enter the amount you want to pay", "Taxes": "Taxes", "Extra Charge": "Extra Charge", "Total": "Total", "Payment Successfully Completed": "Payment Successfully Completed", ":name sent you a payment": ":name sent you a payment", "Donation": "Donation", "Create Donation Payment Link": "Create Donation Payment Link", "Create a donation page for your customers, Transaction Charge is :percentage per donation": "Create a donation page for your customers, Transaction Charge is :percentage per donation", "Goal": "Goal", "Donations": "Donations", "Payment Link Update Successfully": "Payment Link Update Successfully", "Pay with Account Balance": "Pay with Account Balance", "Thanks! We received your payment": "Thanks! We received your payment", "Hello, Guest": "Hello, Guest", "My Wallet": "My Wallet", "Recommended Image Size is :size": "Recommended Image Size is :size", "Single Charge Link Has Been Activated": "Single Charge Link Has Been Activated", "Single Charge Link Has Been Disabled": "Single Charge Link Has Been Disabled", "Api Key Re-Generated Successfully": "Api Key Re-Generated Successfully", "Category created successfully.": "Category created successfully.", "Category updated successfully.": "Category updated successfully.", "Category deleted successfully.": "Category deleted successfully.", "Great! You are trying to deposits. Please follow the next step": "Great! You are trying to deposits. Please follow the next step", "Make Deposit": "Make Deposit", "Hurrah! You are redirect to next step.": "Hurrah! You are redirect to next step.", "Oops! Payment Failed.": "Oops! Payment Failed.", "Digital product created successfully.": "Digital product created successfully.", "Digital product updated successfully.": "Digital product updated successfully.", "Digital product deleted successfully.": "Digital product deleted successfully.", "Invoice Generated Successfully": "Invoice Generated Successfully", "Invoice sent to customer email": "Invoice sent to customer email", "Invoice is already paid": "Invoice is already paid", "Invoice Updated Successfully": "Invoice Updated Successfully", "You are not allowed to delete this invoice": "You are not allowed to delete this invoice", "Invoice Deleted Successfully": "Invoice Deleted Successfully", "KYC Document Submitted Successfully": "KYC Document Submitted Successfully", "You're already submitted": "You're already submitted", "KYC Document Re-Submitted Successfully": "KYC Document Re-Submitted Successfully", "Your OTP is incorrect please check your mail and confirm.": "Your OTP is incorrect please check your mail and confirm.", "Payout request successfully.": "Payout request successfully.", "Physical product created successfully.": "Physical product created successfully.", "Physical product updated successfully.": "Physical product updated successfully.", "Physical product deleted successfully.": "Physical product deleted successfully.", "Plan Created Successfully": "Plan Created Successfully", "Plan Updated Successfully": "Plan Updated Successfully", "Subscription Plan Has Been Activated": "Subscription Plan Has Been Activated", "Subscription Plan Has Been Disabled": "Subscription Plan Has Been Disabled", "Business profile updated successfully.": "Business profile updated successfully.", "Old password is wrong.": "Old password is wrong.", "User not found.": "User not found.", "You can't send request yourself.": "You can't send request yourself.", "Request send successfully.": "Request send successfully.", "Money request has been canceled.": "Money request has been canceled.", "Insufficient balance. Please deposit now.": "Insufficient balance. Please deposit now.", "Shipping fee created successfully.": "Shipping fee created successfully.", "Shipping fee updated successfully.": "Shipping fee updated successfully.", "Shipping fee deleted successfully.": "Shipping fee deleted successfully.", "You are not allowed to delete. Because it has :number orders": "You are not allowed to delete. Because it has :number orders", "Single Charge Deleted Successfully": "Single Charge Deleted Successfully", "Store front created successfully.": "Store front created successfully.", "Store front updated successfully.": "Store front updated successfully.", "Store front deleted successfully.": "Store front deleted successfully.", "Support Ticket Created Successfully": "Support Ticket Created Successfully", "Ticket status has been closed": "Ticket status has been closed", "Reply Sent Successfully": "<PERSON><PERSON> Successfully", "Ticket Deleted Successfully": "Ticket Deleted Successfully", "Transfer successful. Beneficiary created  successfully": "Transfer successful. Beneficiary created  successfully", "Transfer successful.": "Transfer successful.", "Something was wrong, Please contact with author.": "Something was wrong, Please contact with author.", "Insufficient balance.": "Insufficient balance.", "Your password is wrong.": "Your password is wrong.", "Transfer money accepted successfully.": "Transfer money accepted successfully.", "Transfer money has been canceled.": "Transfer money has been canceled.", "Beneficiary deleted successfully.": "Beneficiary deleted successfully.", "Website created successfully.": "Website created successfully.", "Website updated successfully.": "Website updated successfully.", "Website deleted successfully.": "Website deleted successfully.", "About Page Updated Successfully": "About Page Updated Successfully", "FAQ Created Successfully": "FAQ Created Successfully", "FAQ Updated Successfully": "FAQ Updated Successfully", "FAQ Deleted Successfully": "FAQ Deleted Successfully", "Security Section Updated": "Security Section Updated", "Please Upload Logo Image": "Please Upload Logo Image", "Please Upload Favicon Image": "Please Upload Favicon Image", "Logo Setting Updated": "Logo Setting Updated", "Terms of Service Page Edited": "Terms of Service Page Edited", "Profile Updated Successfully": "Profile Updated Successfully", "The current password and the new password cannot be the same": "The current password and the new password cannot be the same", "Password Changed Successfully": "Password Changed Successfully", "Oops! The current password doesn't match": "Oops! The current password doesn't match", "Cache Cleared Successfully": "<PERSON><PERSON> Cleared Successfully", "Role Successfully Assigned": "Role Successfully Assigned", "Bank created successfully.": "Bank created successfully.", "Bank updated successfully.": "Bank updated successfully.", "Banks Deleted Successfully": "Banks Deleted Successfully", "Deleted Successfully": "Deleted Successfully", "Cron Setting Updated Successfully": "Cron Setting Updated Successfully", "Currency Created Successfully": "Currency Created Successfully", "Currency Updated Successfully": "Currency Updated Successfully", "Default currency is not deletable": "Default currency is not deletable", "You are not allowed to delete :type because it has :number :child .": "You are not allowed to delete :type because it has :number :child .", "Currency Deleted Successfully": "Currency Deleted Successfully", "Currency is already default": "Currency is already default", ":name Set As Default Currency": ":name Set As Default <PERSON>", "Email Sent Successfully": "<PERSON><PERSON> Successfully", "Kyc Method Added Successfully": "Kyc Method Added Successfully", "Kyc Method Updated Successfully": "Kyc Method Updated Successfully", "Kyc Method Deleted Successfully": "Kyc Method Deleted Successfully", "KYC Verification Successfully :status": "KYC Verification Successfully :status", "KYC Request Deleted Successfully": "KYC Request Deleted Successfully", "KYC Requests Deleted Successfully": "KYC Requests Deleted Successfully", "Language Created Successfully": "Language Created Successfully", "Phrase Updated Successfully": "Phrase Updated Successfully", "Language Deleted Successfully": "Language Deleted Successfully", "New Phrase Added Successfully": "New Phrase Added Successfully", "Subscription Titles Updated Successfully": "Subscription Titles Updated Successfully", "Oops! Please select Any Status.": "Oops! Please select Any Status.", "Oops! Please select Any Status.Please select any checkbox.": "Oops! Please select Any Status.Please select any checkbox.", "Page Successfully Deleted": "Page Successfully Deleted", "Payout approved successfully.": "Payout approved successfully.", "Payout rejected successfully.": "Payout rejected successfully.", "Payout deleted successfully": "Payout deleted successfully", "Promotional email sent to all users": "Promotional email sent to all users", "Review Created Successfully": "Review Created Successfully", "Review Updated Successfully": "Review Updated Successfully", "Review Deleted Successfully": "Review Deleted Successfully", "Role Created Successfully": "Role Created Successfully", "You are not allowed to mess with Super Admin": "You are not allowed to mess with Super Admin", "Role Update Successfully": "Role Update Successfully", "Role Deleted Successfully": "Role Deleted Successfully", "Successfully Updated": "Successfully Updated", "Unsubscribe Successful": "Unsubscribe Successful", "Subscriber Permanently Deleted": "Subscriber Permanently Deleted", "Support has been :status": "Support has been :status", "Reply sent successfully": "Reply sent successfully", "Tax Created Successfully": "Tax Created Successfully", "Tax Updated Successfully": "Tax Updated Successfully", "Tax Deleted Successfully": "Tax Deleted Successfully", "User Created Successfully": "User Created Successfully", "User Updated Successfully": "User Updated Successfully", "User Deleted Successfully": "User Deleted Successfully", "You are logged in as :name": "You are logged in as :name", "Withdraw approved successfully.": "Withdraw approved successfully.", "Withdraw rejected successfully.": "Withdraw rejected successfully.", "Withdraw deleted successfully": "Withdraw deleted successfully", "Product added to the cart": "Product added to the cart", "Product has been removed from cart.": "Product has been removed from cart.", "Cart product updated successfully.": "Cart product updated successfully.", "The requirement donation amount is exceeds": "The requirement donation amount is exceeds", "Transaction Already Paid": "Transaction Already Paid", "Insufficient Funds! Please Deposit": "Insufficient Funds! Please Deposit", "You are already subscribe to this plan": "You are already subscribe to this plan", "Subscription Payment sent to :name": "Subscription Payment sent to :name", "Subscription Payment received from :name": "Subscription Payment received from :name", "Subscription Purchased Successfully": "Subscription Purchased Successfully", "Logged Out Successfully": "Logged Out Successfully", "Update Api Keys": "Update Api Keys", "Public Key": "Public Key", "API Keys": "API Keys", "Public key copied to clipboard": "Public key copied to clipboard", "Secret Key": "Secret Key", "Secret key copied to clipboard": "Secret key copied to clipboard", "Generate New Keys": "Generate New Keys", "Secret key": "Secret key", "Categories": "Categories", "Categories list": "Categories list", "Add new category": "Add new category", "Category name": "Category name", "S / N": "S / N", "Add category": "Add category", "Close": "Close", "Name of Category": "Name of Category", "Create Category": "Create Category", "Edit category": "Edit category", "Update category": "Update category", "Name of category": "Name of category", "Charges": "Charges", "Amount / Reason": "Amount / Reason", "Reason": "Reason", "Created": "Created", "or": "or", "Logout": "Logout", "Transactions Log": "Transactions Log", "Credit/Debit": "Credit/Debit", "S/N": "S/N", "Date": "Date", "No Earning History": "No Earning History", "We couldn't find any earning log to this account": "We couldn't find any earning log to this account", "API Documentation": "API Documentation", "Our documentation contains what you need to integrate Boompay in your website.": "Our documentation contains what you need to integrate Boompay in your website.", "Go to Docs": "Go to Docs", "Your Keys": "Your Keys", "Also available in <a href=%22#%22 class=%22text-primary%22>Settings > API Keys</a>": "Also available in <a href=\"#\" class=\"text-primary\">Settings > API Keys</a>", "PUBLIC KEY": "PUBLIC KEY", ":name copied to clipboard": ":name copied to clipboard", "SECRET KEY": "SECRET KEY", "Download": "Download", "Total Payout": "Total Payout", "Upgrade Account": "Upgrade Account", "Revenue": "Revenue", "All Payouts": "All Payouts", "Make Deposits": "Make Deposits", "Select a payment method": "Select a payment method", "Deposit Amount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "Gateway Rate": "Gateway Rate", "Payable": "Payable", "Enter your phone number": "Enter your phone number", "Upload Attachment": "Upload Attachment", "Payment Instructions": "Payment Instructions", "Make Payment": "Make Payment", "Deposits": "Deposits", "Deposits list": "Deposits list", "Make deposit": "Make deposit", "Trx ID": "Trx ID", "Payment Method": "Payment Method", "Enter amount": "Enter amount", "Digital Products": "Digital Products", "Product digital create": "Product digital create", "View list": "View list", "The name of your product": "The name of your product", "Price": "Price", "Category": "Category", "Select Category": "Select Category", "Download Link": "Download Link", "Select Store": "Select Store", "Product description": "Product description", "Confirm message": "Confirm message", "Confirmation message": "Confirmation message", "Create Product": "Create Product", "Digital Products Edit": "Digital Products Edit", "Update Product": "Update Product", "Digital products list": "Digital products list", "Add digital product": "Add digital product", "No Product Found": "No Product Found", "We couldn't find any product to this account": "We couldn't find any product to this account", "Create New Payment Link": "Create New Payment Link", "Payment link name": "Payment link name", "Single Charge allows you to create payment links for your customers, Transaction Charge is 2.3% per transaction": "Single Charge allows you to create payment links for your customers, Transaction Charge is 2.3% per transaction", "Create Invoice": "Create Invoice", "Create invoice": "Create invoice", "Invoice charge is :percentage. Invoice is charged when invoice is paid by client.": "Invoice charge is :percentage. Invoice is charged when invoice is paid by client.", "Item Name": "Item Name", "Enter invoice title": "Enter invoice title", "Quantity": "Quantity", "Amount per item": "Amount per item", "Customer Email": "Customer <PERSON><PERSON>", "Enter customer email": "Enter customer email", "Due Date": "Due Date", "Tax": "Tax", "Enter tax in percentage": "Enter tax in percentage", "Discount": "Discount", "Enter discount in percentage": "Enter discount in percentage", "Notes": "Notes", "Invoice note (Optional)": "Invoice note (Optional)", "Edit Invoice": "Edit Invoice", "Edit invoice": "Edit invoice", "Update Invoice": "Update Invoice", "Invoice": "Invoice", "Recipient": "Recipient", "Is Paid": "<PERSON>", ":percentage %": ":percentage%", "Paid": "Paid", "Unpaid": "Unpaid", "Copy to Clipboard": "Copy to Clipboard", "No Invoice Found": "No Invoice Found", "We couldn't find any invoice to this account": "We couldn't find any invoice to this account", "Statistics": "Statistics", "Received": "Received", "Pending": "Pending", "Invoice # :id": "Invoice # :id", "Issue Date: :date": "Issue Date: :date", "Due Date: :date": "Due Date: :date", "Status:": "Status:", "To:": "To:", "Item": "<PERSON><PERSON>", "Sub Total": "Sub Total", "All Invoices": "All Invoices", "Resend": "Resend", "Kyc Documents": "Kyc Documents", "Note": "Note", "No Kyc Methods Found": "No Kyc Methods Found", "Submit New Document": "Submit New Document", "You are verified": "You are verified", "You are not verified": "You are not verified", "Method": "Method", "Documents": "Documents", "Approved": "Approved", "Rejected": "Rejected", "Re-Submitted": "Re-Submitted", "Re Submit": "Re Submit", "Resubmit": "Resubmit", "Old Image": "Old Image", "Verification Status": "Verification Status", "Submitted At": "Submitted At", "Rejected At": "Rejected At", "Label": "Label", "Value": "Value", "Orders": "Orders", "Orders list": "Orders list", "Name / email / invoice / amount": "Name / email / invoice / amount", "Store name": "Store name", "Invoice no": "Invoice no", "Product type": "Product type", "Cancel": "Cancel", "Store": "Store", "View order": "View order", "Product": "Product", "Product Type": "Product Type", "Total Price": "Total Price", "PayStack Payment": "PayStack Payment", "Payment Mode": "Payment Mode", "PayStack": "PayStack", "Pay Now": "Pay Now", "Payu": "<PERSON><PERSON>", "RazorPay Payment": "RazorPay Payment", "Razorpay": "Razorpay", "Stripe Payment": "Stripe Payment", "Stripe": "Stripe", "Credit or debit card": "Credit or debit card", "Submit Payment": "Submit Payment", "Otp for payout request": "Otp for payout request", "Withdraw List": "Withdraw List", "An OTP has been sent to your mail. Please check and confirm.": "An OTP has been sent to your mail. Please check and confirm.", "OTP": "OTP", "Confirm": "Confirm", "Current balance is": "Current balance is", "Payout request amount": "Payout request amount", "Total charge": "Total charge", "Available amount": "Available amount", "Payout": "Payout", "Withdraw Request": "Withdraw Request", "No Payout Request": "No Payout Request", "We couldn't find any payout request to this account": "We couldn't find any payout request to this account", "Payout Details": "Payout Details", "Payout info": "Payout info", "Account Info": "Account Info", "Routing Number": "Routing Number", "Bank info": "Bank info", "Bank Name": "Bank Name", "Bank Code": "Bank Code", "Create physical product": "Create physical product", "Product create": "Product create", "Physical product edit": "Physical product edit", "Physical Products": "Physical Products", "Physical products list": "Physical products list", "Add physical products": "Add physical products", "Plan create": "Plan create", "Create New Plan": "Create New Plan", "Select Interval": "Select Interval", "Hourly": "Hourly", "Daily": "Daily", "Weekly": "Weekly", "Monthly": "Monthly", "Quarterly": "Quarterly", "Every 6 Months": "Every 6 Months", "Yearly": "Yearly", "Number of times to charge a subscriber?": "Number of times to charge a subscriber?", "Leave empty to charge subscriber indefinitely": "Leave empty to charge subscriber indefinitely", "Create Plan": "Create Plan", "Edit Plan": "Edit Plan", "Update Plan": "Update Plan", "Plans": "Plans", "Subscription Payment": "Subscription Payment", "Active/Expired": "Active/Expired", "URL": "URL", "Subscribers": "Subscribers", "No Subscription Plan Found": "No Subscription Plan Found", "We couldn't find any Subscription Plan to this account": "We couldn't find any Subscription Plan to this account", "Subscriber": "Subscriber", "Expire At": "Expire At", "Auto Renew": "Auto Renew", "Renew": "<PERSON>w", "Yes": "Yes", "No": "No", "Update business profile": "Update business profile", "Avatar": "Avatar", "Address": "Address", "Old password": "Old password", "New password": "New password", "Qr Payment": "Qr Payment", "Qr Payment List": "Qr Payment List", "TRX": "TRX", "Request Money": "Request Money", "Create Request": "Create Request", "Email / amount": "Email / amount", "To": "To", "PENDING": "PENDING", "APPROVED": "APPROVED", "CANCELED": "CANCELED", "No Money Request": "No Money Request", "We couldn't find any payouts money request to this account": "We couldn't find any payouts money request to this account", "Send money request": "Send money request", "Make sure he/she have an account on": "Make sure he/she have an account on", "Transfer charge is": "Transfer charge is", "per transaction. Charge will be taken from sender": "per transaction. Charge will be taken from sender", "Send Request": "Send Request", "View money request": "View money request", "Demo": "Demo", "Sender information": "Sender information", "Receiver information": "Receiver information", "Shipping Regions & Rate": "Shipping Regions & Rate", " Shipping Regions & Rate": "Shipping Regions & Rate", "New Add Shipping Fee": "New Add Shipping Fee", "Region": "Region", "Create Shipping fee": "Create Shipping fee", "Dhaka, Bangladesh": "Dhaka, Bangladesh", "Create Shipping Fee": "Create Shipping Fee", "Edit Shipping fee": "Edit Shipping fee", "Update Shipping Fee": "Update Shipping Fee", "Edit Single Charge": "Edit Single Charge", "Update Link": "Update Link", "Single Charges": "Single Charges", "Store fronts": "Store fronts", "Store fronts create": "Store fronts create", "The name of your store": "The name of your store", "Store Name": "Store Name", "Shipping Status": "Shipping Status", "Disabled": "Disabled", "Delivery Note": "Delivery Note", "Required": "Required", "Optional": "Optional", "Physical": "Physical", "Digital": "Digital", "Store Description": "Store Description", "Create Store": "Create Store", "Store fronts edit": "Store fronts edit", "Store fronts list": "Store fronts list", "Create store fronts": "Create store fronts", "Store name2": "Store name", "Shopping status": "Shopping status", "Copy Link": "Copy Link", "ACTIVE": "ACTIVE", "DEACTIVATE": "DEACTIVATE", "Plan": "Plan", "Reference ID": "Reference ID", "Expiring Date": "Expiring Date", "Renewal": "Renewal", "Open Support Ticket": "Open Support Ticket", "Open ticket": "Open ticket", "New Ticket": "New Ticket", "Reference": "Reference", "Transaction reference number": "Transaction reference number", "Priority": "Priority", "Low": "Low", "Medium": "Medium", "High": "High", "Type": "Type", "Subscription": "Subscription", "Money Transfer": "Money Transfer", "Settlement": "Settlement", "Bank transfer": "Bank transfer", "Deposit": "<PERSON><PERSON><PERSON><PERSON>", "Others": "Others", "Details": "Details", "Select Images for attachment": "Select Images for attachment", "Open Ticket": "Open Ticket", "Closed": "Closed", "Open": "Open", "No Ticket Found": "No Ticket Found", "We couldn't find any ticket to this account": "We couldn't find any ticket to this account", "Attachments": "Attachments", "Log": "Log", "Administrator": "Administrator", "Reply": "Reply", "Enter your message...": "Enter your message...", "Qr Code": "Qr Code", "Bank Transfer": "Bank Transfer", "Website": "Website", "Your Subscriptions": "Your Subscriptions", "Transfer money": "Transfer money", "Email address": "Email address", "Select": "Select", "Transfer Money": "Transfer Money", "Send Money": "Send Money", "Beneficiary": "Beneficiary", "Sent": "<PERSON><PERSON>", "Business": "Business", "Confirmed": "Confirmed", "Accepted": "Accepted", "Canceled": "Canceled", "Accept": "Accept", "No Transfer Request": "No Transfer Request", "We couldn't find any transfer request to this account": "We couldn't find any transfer request to this account", "Returned": "Returned", "No Beneficiary Found": "No Beneficiary Found", "We couldn't find any beneficiary to this account": "We couldn't find any beneficiary to this account", "Merchant": "Merchant", "Add New Website": "Add New Website", "View List": "View List", "Merchant Name": "Merchant Name", "Website Mode": "Website Mode", "Mode": "Mode", "Live": "Live", "Test": "Test", "Contact Email": "Contact Email", "Send Notifications To": "Send Notifications To", "If provided, this email address will get transaction notification": "If provided, this email address will get transaction notification", "Message After Payment": "Message After Payment", "Create Merchant": "Create Merchant", "Website Integration Documentation": "Website Integration Documentation", "Website Integration": "Website Integration", "Documentation": "Documentation", "Integrating Website Payment": "Integrating Website Payment", "This document will introduce you to all the basic information you need to better understand our technologies. To start receiving payment on your website, or you need to do is copy the html form code below to your website page": "This document will introduce you to all the basic information you need to better understand our technologies. To start receiving payment on your website, or you need to do is copy the html form code below to your website page", "Receiving money on your website is now easy with simple integration at a fee of 2% per transaction.": "Receiving money on your website is now easy with simple integration at a fee of 2% per transaction.", "Form element": "Form element", "COPY CODE": "COPY CODE", "Verifying payment": "Verifying payment", "Depending on your callback url is not fully secure, ensure you verify payment with our api before going further.": "Depending on your callback url is not fully secure, ensure you verify payment with our api before going further.", "Verify payment": "Verify payment", "Successful Json Callback": "Successful <PERSON><PERSON>", "Requirements": "Requirements", "1.": "1.", "token": "token", "string": "string", "Used to authorize a transaction": "Used to authorize a transaction", "This is a callback endpoint you provide ": "This is a callback endpoint you provide", "This is the merchant reference tied to a transaction": "This is the merchant reference tied to a transaction", "int [Above 0.50 cents": "int [Above 0.50 cents]", "Cost of Item Purchased": "Cost of <PERSON><PERSON>chased", "Email of Client making payment": "Email of Client making payment", "First name of Client making payment": "First name of Client making payment", "last name of Client making payment": "last name of Client making payment", "Title of transaction": "Title of transaction", "Description of what transaction is for": "Description of what transaction is for", "This is the currency the transaction list should come in :code": "This is the currency the transaction list should come in :code", "Quantity of Item being paid for": "Quantity of Item being paid for", "Edit Website": "Edit Website", "Website Name": "Website Name", "Select Mode": "Select Mode", "Update Merchant": "Update Merchant", "Merchant name": "Merchant name", "Merchant Key": "Merchant Key", "Notify email": "Notify email", ":name key copied to clipboard": ":name key copied to clipboard", "LIVE": "LIVE", "TEST": "TEST", "Live Now": "Live Now", "Test Transactions": "Test Transactions", "No Website Found": "No Website Found", "We couldn't find any website to this account": "We couldn't find any website to this account", "IP Address": "IP Address", "Paid At": "<PERSON><PERSON>", "Reference Code": "Reference Code", "Gateway": "Gateway", "We couldn't find any transactions to this website": "We couldn't find any transactions to this website", "Purchase payment": "Purchase payment", "Total price": "Total price", "Pay": "Pay", "Order invoice page": "Order invoice page", "Data :": "Data :", "by :name at :datetime": "by :name at :datetime", "Single Charge Amount": "Single Charge Amount", "(by :name on :dateTime)": "(by :name on :dateTime)", ":amount GOAL": ":amount GOAL", ":raised Raised, Donations (:donations)": ":raised Raised, Donations (:donations)", "Donate for helpless people": "Donate for helpless people", "Please Select A Gateway": "Please Select A Gateway", "Donation goal is completed": "Donation goal is completed", "Donors List:": "Donors List:", ":amount - :datetime": ":amount - :datetime", ":quantity = :total": ":quantity = :total", "Payment sent to :name": "Payment sent to :name", "Payment received from :name": "Payment received from :name", "Anonymous": "Anonymous", "Publicly": "Publicly", "Donation sent to :name": "Donation sent to :name", "Donation received from :name": "Donation received from :name", "Thank you :name for donation.": "Thank you :name for donation.", "Back To Home": "Back To Home", "Invoice Payment": "Invoice Payment", "Invoice Payment sent to :name": "Invoice Payment sent to :name", "Invoice Payment received from :name": "Invoice Payment received from :name", "Thank you :name for payment.": "Thank you :name for payment.", "You Need Add This Command In Your Supervisor And Also Make QUEUE_MAIL On From System Settings To Mail Configuration.": "You Need Add This Command In Your Supervisor And Also Make QUEUE_MAIL On From System Settings To Mail Configuration.", "Send Mail with Queue": "Send Mail with Queue", "Note:": "Note:", "Merchant: :merchant": "<strong> Merchant:</strong> :merchant", "Amount: :amount": "<strong> Amount:</strong> :amount", "Quantity: :quantity": "<strong> Quantity:</strong> :quantity", "Total: :total": "<strong> Total:</strong> :total", "by :name on :datetime": "by :name on :datetime", "Test Mode": "Test Mode", "Merchant Payment": "Merchant Payment", "Merchant payment received from :name": "Merchant payment received from :name", "Merchant payment sent to :name": "Merchant payment sent to :name", "Order Created Successfully": "Order Created Successfully", "Search...": "Search...", "Times": "Times", "Transaction type didn't match, Please check the transaction type.": "Transaction type didn't match, Please check the transaction type.", "Type a reply ...": "Type a reply ...", "Subscribe Now": "Subscribe Now", "The minimum transaction amount is :amount": "The minimum transaction amount is :amount", "The maximum transaction amount is :amount": "The maximum transaction amount is :amount", "Using Postman": "Using <PERSON>man", "Max. Amount": "<PERSON><PERSON>", "Min. Amount": "<PERSON><PERSON>", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Logo": "Logo", "SL.": "SL.", "Namespace": "Namespace", "Add New Gateway": "Add New Gateway", "Gateway List": "Gateway List", "Create New Gateway": "Create New Gateway", "Create New Payment Gateway": "Create New Payment Gateway", "Rate": "Rate", "Payment Instruction": "Payment Instruction", "Minimum Amount": "Minimum Amount", "Minimum transaction amount": "Minimum transaction amount", "Maximum Amount": "Maximum Amount", "Maximum transaction amount": "Maximum transaction amount", "Sandbox Mode": "Sandbox Mode", "Enable": "Enable", "Deactivate": "Deactivate", "Add Item": "Add Item", "Subtotal": "Subtotal", "Credit": "Credit", "Debit": "Debit", "Ticket Number: :number": "Ticket Number: :number", "Reference Code: :code": "Reference Code: :code", "Type: :type": "Type: :type", "Features": "Features", "Enter feature title": "Enter feature title", "feature title": "feature title", "Plan Information": "Plan Information", "Duration": "Duration", "Pricing": "Pricing", "Are you sure to subscribe this plan?": "Are you sure to subscribe this plan?", "Owner": "Owner", "Disable Now": "Disable Now", "Enable Now": "Enable Now", "Enabled": "Enabled", "Auto renew :status": "Auto renew :status", "Renewed": "Renewed", ":times times": ":times times", "Blog Created Successfully": "Blog Created Successfully", "Blog Updated Successfully": "Blog Updated Successfully", "Blog Deleted Successfully": "Blog Deleted Successfully", "Oops something wrong": "Oops something wrong", "Page Updated Successfully": "Page Updated Successfully", "Page Deleted Successfully": "Page Deleted Successfully", "Page Created Successfully": "Page Created Successfully", "Page": "Page", "Question": "Question", "Answer": "Answer", "Delete Me": "Delete Me", "Percentage": "Percentage", "Fixed": "Fixed", "Money Request Charge Type": "Money Request Charge Type", "Money Request Charge Rate": "Money Request Charge Rate", "Money Withdraw Charge Type": "Money Withdraw Charge Type", "Money Withdraw Charge Rate": "Money Withdraw Charge Rate", "Money Transfer Charge Type": "Money Transfer Charge Type", "Money Transfer Charge Rate": "Money Transfer Charge Rate", "Bank Transaction Charge Type": "Bank Transaction Charge Type", "installer_messages.title": "Garments Erp Installer", "installer_messages.next": "Next Step", "installer_messages.back": "Previous", "installer_messages.finish": "Install", "installer_messages.forms.errorTitle": "The Following errors occurred:", "installer_messages.welcome.templateTitle": "Welcome", "installer_messages.welcome.title": "Garments Erp Installer", "installer_messages.welcome.message": "Easy Installation and Setup Wizard.", "installer_messages.welcome.next": "Check Requirements", "installer_messages.requirements.templateTitle": "Step 1 | Server Requirements", "installer_messages.requirements.title": "Server Requirements", "installer_messages.requirements.next": "Check Permissions", "installer_messages.permissions.templateTitle": "Step 2 | Permissions", "installer_messages.permissions.title": "Permissions", "installer_messages.permissions.next": "Configure Environment", "installer_messages.environment.menu.templateTitle": "Step 3 | Environment Settings", "installer_messages.environment.menu.title": "Environment Settings", "installer_messages.environment.menu.desc": "Please select how you want to configure the apps .env file.", "installer_messages.environment.menu.wizard-button": "Form Wizard Setup", "installer_messages.environment.menu.classic-button": "Classic Text Editor", "installer_messages.environment.wizard.templateTitle": "Step 3 | Environment Settings | Guided Wizard", "installer_messages.environment.wizard.title": "Guided .env Wizard", "installer_messages.environment.wizard.tabs.environment": "Environment", "installer_messages.environment.wizard.tabs.database": "Database", "installer_messages.environment.wizard.tabs.application": "Application", "installer_messages.environment.wizard.form.name_required": "An environment name is required.", "installer_messages.environment.wizard.form.app_name_label": "Website name", "installer_messages.environment.wizard.form.app_name_placeholder": "Enter your website name", "installer_messages.environment.wizard.form.app_environment_label": "App Environment", "installer_messages.environment.wizard.form.app_environment_label_local": "Local", "installer_messages.environment.wizard.form.app_environment_label_developement": "Development", "installer_messages.environment.wizard.form.app_environment_label_qa": "Qa", "installer_messages.environment.wizard.form.app_environment_label_production": "Production", "installer_messages.environment.wizard.form.app_environment_label_other": "Other", "installer_messages.environment.wizard.form.app_environment_placeholder_other": "Enter your environment...", "installer_messages.environment.wizard.form.app_debug_label": "App Debug", "installer_messages.environment.wizard.form.app_debug_label_true": "True", "installer_messages.environment.wizard.form.app_debug_label_false": "False", "installer_messages.environment.wizard.form.app_log_level_label": "App Log Level", "installer_messages.environment.wizard.form.app_log_level_label_debug": "debug", "installer_messages.environment.wizard.form.app_log_level_label_info": "info", "installer_messages.environment.wizard.form.app_log_level_label_notice": "notice", "installer_messages.environment.wizard.form.app_log_level_label_warning": "warning", "installer_messages.environment.wizard.form.app_log_level_label_error": "error", "installer_messages.environment.wizard.form.app_log_level_label_critical": "critical", "installer_messages.environment.wizard.form.app_log_level_label_alert": "alert", "installer_messages.environment.wizard.form.app_log_level_label_emergency": "emergency", "installer_messages.environment.wizard.form.app_url_label": "App Url", "installer_messages.environment.wizard.form.app_url_placeholder": "App Url", "installer_messages.environment.wizard.form.db_connection_failed": "Could not connect to the database.", "installer_messages.environment.wizard.form.db_connection_label": "Database Connection", "installer_messages.environment.wizard.form.db_connection_label_mysql": "mysql", "installer_messages.environment.wizard.form.db_connection_label_sqlite": "sqlite", "installer_messages.environment.wizard.form.db_connection_label_pgsql": "pgsql", "installer_messages.environment.wizard.form.db_connection_label_sqlsrv": "sqlsrv", "installer_messages.environment.wizard.form.db_host_label": "Database Host", "installer_messages.environment.wizard.form.db_host_placeholder": "Database Host", "installer_messages.environment.wizard.form.db_port_label": "Database Port", "installer_messages.environment.wizard.form.db_port_placeholder": "Database Port", "installer_messages.environment.wizard.form.db_name_label": "Database Name", "installer_messages.environment.wizard.form.db_name_placeholder": "Database Name", "installer_messages.environment.wizard.form.db_username_label": "Database User Name", "installer_messages.environment.wizard.form.db_username_placeholder": "Database User Name", "installer_messages.environment.wizard.form.db_password_label": "Database Password", "installer_messages.environment.wizard.form.db_password_placeholder": "Database Password", "installer_messages.environment.wizard.form.app_tabs.more_info": "More Info", "installer_messages.environment.wizard.form.app_tabs.broadcasting_title": "Broadcasting, Caching, Session, &amp; Queue", "installer_messages.environment.wizard.form.app_tabs.broadcasting_label": "Broadcast Driver", "installer_messages.environment.wizard.form.app_tabs.broadcasting_placeholder": "Broadcast Driver", "installer_messages.environment.wizard.form.app_tabs.cache_label": "<PERSON><PERSON>", "installer_messages.environment.wizard.form.app_tabs.cache_placeholder": "<PERSON><PERSON>", "installer_messages.environment.wizard.form.app_tabs.session_label": "Session Driver", "installer_messages.environment.wizard.form.app_tabs.session_placeholder": "Session Driver", "installer_messages.environment.wizard.form.app_tabs.queue_label": "Queue Driver", "installer_messages.environment.wizard.form.app_tabs.queue_placeholder": "Queue Driver", "installer_messages.environment.wizard.form.app_tabs.redis_label": "<PERSON><PERSON>", "installer_messages.environment.wizard.form.app_tabs.redis_host": "Redis Host", "installer_messages.environment.wizard.form.app_tabs.redis_password": "Redis Password", "installer_messages.environment.wizard.form.app_tabs.redis_port": "Redis Port", "installer_messages.environment.wizard.form.app_tabs.mail_label": "Mail", "installer_messages.environment.wizard.form.app_tabs.mail_driver_label": "Mail Driver", "installer_messages.environment.wizard.form.app_tabs.mail_driver_placeholder": "Mail Driver", "installer_messages.environment.wizard.form.app_tabs.mail_host_label": "Mail Host", "installer_messages.environment.wizard.form.app_tabs.mail_host_placeholder": "Mail Host", "installer_messages.environment.wizard.form.app_tabs.mail_port_label": "Mail Port", "installer_messages.environment.wizard.form.app_tabs.mail_port_placeholder": "Mail Port", "installer_messages.environment.wizard.form.app_tabs.mail_username_label": "Mail Username", "installer_messages.environment.wizard.form.app_tabs.mail_username_placeholder": "Mail Username", "installer_messages.environment.wizard.form.app_tabs.mail_password_label": "Mail Password", "installer_messages.environment.wizard.form.app_tabs.mail_password_placeholder": "Mail Password", "installer_messages.environment.wizard.form.app_tabs.mail_encryption_label": "Mail Encryption", "installer_messages.environment.wizard.form.app_tabs.mail_encryption_placeholder": "Mail Encryption", "installer_messages.environment.wizard.form.app_tabs.pusher_label": "<PERSON><PERSON><PERSON>", "installer_messages.environment.wizard.form.app_tabs.pusher_app_id_label": "<PERSON><PERSON><PERSON> App Id", "installer_messages.environment.wizard.form.app_tabs.pusher_app_id_palceholder": "<PERSON><PERSON><PERSON> App Id", "installer_messages.environment.wizard.form.app_tabs.pusher_app_key_label": "<PERSON><PERSON><PERSON>pp Key", "installer_messages.environment.wizard.form.app_tabs.pusher_app_key_palceholder": "<PERSON><PERSON><PERSON>pp Key", "installer_messages.environment.wizard.form.app_tabs.pusher_app_secret_label": "<PERSON><PERSON><PERSON> App Secret", "installer_messages.environment.wizard.form.app_tabs.pusher_app_secret_palceholder": "<PERSON><PERSON><PERSON> App Secret", "installer_messages.environment.wizard.form.buttons.setup_database": "Setup Database", "installer_messages.environment.wizard.form.buttons.setup_application": "Setup Application", "installer_messages.environment.wizard.form.buttons.install": "Install", "installer_messages.environment.classic.templateTitle": "Step 3 | Environment Settings | Classic Editor", "installer_messages.environment.classic.title": "Classic Environment Editor", "installer_messages.environment.classic.save": "Save .env", "installer_messages.environment.classic.back": "Use Form Wizard", "installer_messages.environment.classic.install": "Save and Install", "installer_messages.environment.success": "Your .env file settings have been saved.", "installer_messages.environment.errors": "Unable to save the .env file, Please create it manually.", "installer_messages.install": "Install", "installer_messages.installed.success_log_message": "<PERSON><PERSON> In<PERSON>ler successfully INSTALLED on ", "installer_messages.final.title": "Installation Finished", "installer_messages.final.templateTitle": "Installation Finished", "installer_messages.final.finished": "Application has been successfully installed.", "installer_messages.final.migration": "Migration &amp; Seed Console Output:", "installer_messages.final.console": "Application Console Output:", "installer_messages.final.log": "Installation Log Entry:", "installer_messages.final.env": "Final .env File:", "installer_messages.final.exit": "Click here to exit", "installer_messages.updater.title": "<PERSON><PERSON>", "installer_messages.updater.welcome.title": "Welcome To The Updater", "installer_messages.updater.welcome.message": "Welcome to the update wizard.", "installer_messages.updater.overview.title": "Overview", "installer_messages.updater.overview.message": "There is 1 update.|There are :number updates.", "installer_messages.updater.overview.install_updates": "Install Updates", "installer_messages.updater.final.title": "Finished", "installer_messages.updater.final.finished": "Application's database has been successfully updated.", "installer_messages.updater.final.exit": "Click here to exit", "installer_messages.updater.log.success_message": "<PERSON><PERSON> In<PERSON>ler successfully UPDATED on "}